package matroska

import (
	"io"
)

func (p *Parser) parseAttachments(element *EBMLElement) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}

		if child.ID == AttachedFileID {
			attachment, err := p.parseAttachedFile(child)
			if err != nil {
				return err
			}
			p.attachments = append(p.attachments, attachment)
		}
	}

	return nil
}

func (p *Parser) parseAttachedFile(element *EBMLElement) (*Attachment, error) {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	attachment := &Attachment{
		Position: element.Offset,
		Length:   element.Size,
	}

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}

		switch child.ID {
		case FileNameID:
			attachment.Name = child.ReadString()
		case FileDescriptionID:
			attachment.Description = child.ReadString()
		case FileMimeTypeID:
			attachment.MimeType = child.ReadString()
		case FileUIDID:
			uid, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			attachment.UID = uid
		case FileDataID:
			attachment.Position = child.Offset
			attachment.Length = uint64(len(child.Data))
		}
	}

	return attachment, nil
}

func (p *Parser) parseChapters(element *EBMLElement) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}

		if child.ID == EditionEntryID {
			chapters, err := p.parseEditionEntry(child)
			if err != nil {
				return err
			}
			p.chapters = append(p.chapters, chapters...)
		}
	}

	return nil
}

func (p *Parser) parseEditionEntry(element *EBMLElement) ([]*Chapter, error) {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	var chapters []*Chapter

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}

		if child.ID == ChapterAtomID {
			chapter, err := p.parseChapterAtom(child)
			if err != nil {
				return nil, err
			}
			chapters = append(chapters, chapter)
		}
	}

	return chapters, nil
}

func (p *Parser) parseChapterAtom(element *EBMLElement) (*Chapter, error) {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	chapter := &Chapter{
		Enabled: true,
	}

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}

		switch child.ID {
		case ChapterUIDID:
			uid, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			chapter.UID = uid
		case ChapterTimeStartID:
			start, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			chapter.Start = start
		case ChapterTimeEndID:
			end, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			chapter.End = end
		case ChapterFlagHiddenID:
			hidden, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			chapter.Hidden = hidden != 0
		case ChapterFlagEnabledID:
			enabled, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			chapter.Enabled = enabled != 0
		case ChapterSegmentUIDID:
			uid := child.ReadBytes()
			if len(uid) <= 16 {
				copy(chapter.SegmentUID[:], uid)
			}
		case ChapterDisplayID:
			display, err := p.parseChapterDisplay(child)
			if err != nil {
				return nil, err
			}
			chapter.Display = append(chapter.Display, display)
		case ChapterAtomID:
			childChapter, err := p.parseChapterAtom(child)
			if err != nil {
				return nil, err
			}
			chapter.Children = append(chapter.Children, childChapter)
		case ChapterProcessID:
			process, err := p.parseChapterProcess(child)
			if err != nil {
				return nil, err
			}
			chapter.Process = append(chapter.Process, process)
		}
	}

	return chapter, nil
}

func (p *Parser) parseChapterDisplay(element *EBMLElement) (ChapterDisplay, error) {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	var display ChapterDisplay

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return display, err
		}

		switch child.ID {
		case ChapStringID:
			display.String = child.ReadString()
		case ChapLanguageID:
			lang := child.ReadString()
			if len(lang) >= 3 {
				display.Language = lang[:3]
			} else {
				display.Language = lang
			}
		case ChapCountryID:
			country := child.ReadString()
			if len(country) >= 3 {
				display.Country = country[:3]
			} else {
				display.Country = country
			}
		}
	}

	return display, nil
}

func (p *Parser) parseChapterProcess(element *EBMLElement) (ChapterProcess, error) {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	var process ChapterProcess

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return process, err
		}

		switch child.ID {
		case ChapterProcessCodecIDID:
			codecID, err := child.ReadUint()
			if err != nil {
				return process, err
			}
			process.CodecID = uint32(codecID)
		case ChapterProcessPrivateID:
			process.CodecPrivate = child.ReadBytes()
		case ChapterProcessCommandID:
			command, err := p.parseChapterCommand(child)
			if err != nil {
				return process, err
			}
			process.Commands = append(process.Commands, command)
		}
	}

	return process, nil
}

func (p *Parser) parseChapterCommand(element *EBMLElement) (ChapterCommand, error) {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	var command ChapterCommand

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return command, err
		}

		switch child.ID {
		case ChapterProcessTimeID:
			time, err := child.ReadUint()
			if err != nil {
				return command, err
			}
			command.Time = uint32(time)
		case ChapterProcessDataID:
			command.Command = child.ReadBytes()
		}
	}

	return command, nil
}

func (p *Parser) parseTags(element *EBMLElement) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}

		if child.ID == TagID {
			tag, err := p.parseTag(child)
			if err != nil {
				return err
			}
			p.tags = append(p.tags, tag)
		}
	}

	return nil
}

func (p *Parser) parseTag(element *EBMLElement) (*Tag, error) {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	tag := &Tag{}

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}

		switch child.ID {
		case TargetsID:
			targets, err := p.parseTargets(child)
			if err != nil {
				return nil, err
			}
			tag.Targets = append(tag.Targets, targets...)
		case SimpleTagID:
			simpleTag, err := p.parseSimpleTag(child)
			if err != nil {
				return nil, err
			}
			tag.SimpleTags = append(tag.SimpleTags, simpleTag)
		}
	}

	return tag, nil
}

func (p *Parser) parseTargets(element *EBMLElement) ([]Target, error) {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	var targets []Target
	target := Target{}

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}

		switch child.ID {
		case TargetTypeValueID:
			typeValue, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			target.Type = uint32(typeValue)
		case TagTrackUIDID:
			uid, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			target.UID = uid
		}
	}

	targets = append(targets, target)
	return targets, nil
}

func (p *Parser) parseSimpleTag(element *EBMLElement) (SimpleTag, error) {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	var simpleTag SimpleTag
	simpleTag.Language = "und"

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return simpleTag, err
		}

		switch child.ID {
		case TagNameID:
			simpleTag.Name = child.ReadString()
		case TagStringID:
			simpleTag.Value = child.ReadString()
		case TagLanguageID:
			lang := child.ReadString()
			if len(lang) >= 3 {
				simpleTag.Language = lang[:3]
			} else {
				simpleTag.Language = lang
			}
		case TagDefaultID:
			def, err := child.ReadUint()
			if err != nil {
				return simpleTag, err
			}
			simpleTag.Default = def != 0
		}
	}

	return simpleTag, nil
}

func (p *Parser) parseCues(element *EBMLElement) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}

		if child.ID == CuePointID {
			cue, err := p.parseCuePoint(child)
			if err != nil {
				return err
			}
			p.cues = append(p.cues, cue)
		}
	}

	return nil
}

func (p *Parser) parseCuePoint(element *EBMLElement) (*Cue, error) {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	cue := &Cue{}

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}

		switch child.ID {
		case CueTimeID:
			time, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			cue.Time = time
		case CueDurationID:
			duration, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			cue.Duration = duration
		case CueTrackPositionsID:
			if err := p.parseCueTrackPositions(child, cue); err != nil {
				return nil, err
			}
		}
	}

	return cue, nil
}

func (p *Parser) parseCueTrackPositions(element *EBMLElement, cue *Cue) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})

	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}

		switch child.ID {
		case CueTrackID:
			track, err := child.ReadUint()
			if err != nil {
				return err
			}
			cue.Track = uint8(track)
		case CueClusterPositionID:
			pos, err := child.ReadUint()
			if err != nil {
				return err
			}
			cue.Position = pos
		case CueRelativePositionID:
			relPos, err := child.ReadUint()
			if err != nil {
				return err
			}
			cue.RelativePosition = relPos
		case CueBlockNumberID:
			block, err := child.ReadUint()
			if err != nil {
				return err
			}
			cue.Block = block
		}
	}

	return nil
}

func (p *Parser) parseClusterInfo(element *EBMLElement) (*ClusterInfo, error) {
	cluster := &ClusterInfo{
		Position: element.Offset,
		Size:     element.Size,
	}

	reader := NewEBMLReader(&bytesReader{data: element.Data[:min(len(element.Data), 1024)]})

	for reader.Position() < uint64(len(element.Data)) && reader.Position() < 1024 {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}

		switch child.ID {
		case TimecodeID:
			timecode, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			cluster.Timecode = timecode
		case PrevSizeID:
			prevSize, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			cluster.PrevSize = prevSize
		default:
			return cluster, nil
		}
	}

	return cluster, nil
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
